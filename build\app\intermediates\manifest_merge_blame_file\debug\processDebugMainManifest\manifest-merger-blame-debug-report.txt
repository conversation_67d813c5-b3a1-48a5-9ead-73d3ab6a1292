1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.salamt.tifli.salamt_tifli"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="36" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\Development Yassine\flutter\Salamt_Tifli\android\app\src\debug\AndroidManifest.xml:6:5-66
15-->D:\Development Yassine\flutter\Salamt_Tifli\android\app\src\debug\AndroidManifest.xml:6:22-64
16    <!--
17     Required to query activities that can process text, see:
18         https://developer.android.com/training/package-visibility and
19         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
20
21         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
22    -->
23    <queries>
23-->D:\Development Yassine\flutter\Salamt_Tifli\android\app\src\main\AndroidManifest.xml:39:5-44:15
24        <intent>
24-->D:\Development Yassine\flutter\Salamt_Tifli\android\app\src\main\AndroidManifest.xml:40:9-43:18
25            <action android:name="android.intent.action.PROCESS_TEXT" />
25-->D:\Development Yassine\flutter\Salamt_Tifli\android\app\src\main\AndroidManifest.xml:41:13-72
25-->D:\Development Yassine\flutter\Salamt_Tifli\android\app\src\main\AndroidManifest.xml:41:21-70
26
27            <data android:mimeType="text/plain" />
27-->D:\Development Yassine\flutter\Salamt_Tifli\android\app\src\main\AndroidManifest.xml:42:13-50
27-->D:\Development Yassine\flutter\Salamt_Tifli\android\app\src\main\AndroidManifest.xml:42:19-48
28        </intent>
29    </queries>
30
31    <uses-permission android:name="android.permission.VIBRATE" />
31-->[:flutter_local_notifications] D:\Development Yassine\flutter\Salamt_Tifli\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
31-->[:flutter_local_notifications] D:\Development Yassine\flutter\Salamt_Tifli\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-63
32    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
32-->[:flutter_local_notifications] D:\Development Yassine\flutter\Salamt_Tifli\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
32-->[:flutter_local_notifications] D:\Development Yassine\flutter\Salamt_Tifli\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-74
33    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
33-->[androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3f3f2e728699f00eba298007738e88a2\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:22:5-79
33-->[androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3f3f2e728699f00eba298007738e88a2\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:22:22-76
34
35    <permission
35-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
36        android:name="com.salamt.tifli.salamt_tifli.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
36-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
37        android:protectionLevel="signature" />
37-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
38
39    <uses-permission android:name="com.salamt.tifli.salamt_tifli.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
39-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
39-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
40
41    <application
42        android:name="android.app.Application"
43        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
43-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
44        android:debuggable="true"
45        android:extractNativeLibs="true"
46        android:icon="@mipmap/ic_launcher"
47        android:label="salamt_tifli" >
48        <activity
49            android:name="com.salamt.tifli.salamt_tifli.MainActivity"
50            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
51            android:exported="true"
52            android:hardwareAccelerated="true"
53            android:launchMode="singleTop"
54            android:taskAffinity=""
55            android:theme="@style/LaunchTheme"
56            android:windowSoftInputMode="adjustResize" >
57
58            <!--
59                 Specifies an Android theme to apply to this Activity as soon as
60                 the Android process has started. This theme is visible to the user
61                 while the Flutter UI initializes. After that, this theme continues
62                 to determine the Window background behind the Flutter UI.
63            -->
64            <meta-data
65                android:name="io.flutter.embedding.android.NormalTheme"
66                android:resource="@style/NormalTheme" />
67
68            <intent-filter>
69                <action android:name="android.intent.action.MAIN" />
70
71                <category android:name="android.intent.category.LAUNCHER" />
72            </intent-filter>
73        </activity>
74        <!--
75             Don't delete the meta-data below.
76             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
77        -->
78        <meta-data
79            android:name="flutterEmbedding"
80            android:value="2" />
81
82        <service
82-->[:geolocator_android] D:\Development Yassine\flutter\Salamt_Tifli\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:56
83            android:name="com.baseflow.geolocator.GeolocatorLocationService"
83-->[:geolocator_android] D:\Development Yassine\flutter\Salamt_Tifli\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-77
84            android:enabled="true"
84-->[:geolocator_android] D:\Development Yassine\flutter\Salamt_Tifli\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-35
85            android:exported="false"
85-->[:geolocator_android] D:\Development Yassine\flutter\Salamt_Tifli\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
86            android:foregroundServiceType="location" />
86-->[:geolocator_android] D:\Development Yassine\flutter\Salamt_Tifli\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-53
87
88        <activity
88-->[:url_launcher_android] D:\Development Yassine\flutter\Salamt_Tifli\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
89            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
89-->[:url_launcher_android] D:\Development Yassine\flutter\Salamt_Tifli\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
90            android:exported="false"
90-->[:url_launcher_android] D:\Development Yassine\flutter\Salamt_Tifli\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
91            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
91-->[:url_launcher_android] D:\Development Yassine\flutter\Salamt_Tifli\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
92
93        <uses-library
93-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
94            android:name="androidx.window.extensions"
94-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
95            android:required="false" />
95-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
96        <uses-library
96-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
97            android:name="androidx.window.sidecar"
97-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
98            android:required="false" />
98-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
99
100        <activity
100-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a3d296532cc5acbfa6d00cce05e38839\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:9-22:45
101            android:name="com.google.android.gms.common.api.GoogleApiActivity"
101-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a3d296532cc5acbfa6d00cce05e38839\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:19-85
102            android:exported="false"
102-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a3d296532cc5acbfa6d00cce05e38839\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:22:19-43
103            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
103-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a3d296532cc5acbfa6d00cce05e38839\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:21:19-78
104
105        <meta-data
105-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
106            android:name="com.google.android.gms.version"
106-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
107            android:value="@integer/google_play_services_version" />
107-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
108
109        <provider
109-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
110            android:name="androidx.startup.InitializationProvider"
110-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
111            android:authorities="com.salamt.tifli.salamt_tifli.androidx-startup"
111-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
112            android:exported="false" >
112-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
113            <meta-data
113-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
114                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
114-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
115                android:value="androidx.startup" />
115-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
116            <meta-data
116-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
117                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
117-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
118                android:value="androidx.startup" />
118-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
119        </provider>
120
121        <receiver
121-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
122            android:name="androidx.profileinstaller.ProfileInstallReceiver"
122-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
123            android:directBootAware="false"
123-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
124            android:enabled="true"
124-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
125            android:exported="true"
125-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
126            android:permission="android.permission.DUMP" >
126-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
127            <intent-filter>
127-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
128                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
128-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
128-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
129            </intent-filter>
130            <intent-filter>
130-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
131                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
131-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
131-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
132            </intent-filter>
133            <intent-filter>
133-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
134                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
134-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
134-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
135            </intent-filter>
136            <intent-filter>
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
137                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
138            </intent-filter>
139        </receiver>
140    </application>
141
142</manifest>
