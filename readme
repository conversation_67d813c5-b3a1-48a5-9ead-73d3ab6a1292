
فكرة تطبيق "سلامة طفلي" رائعة ومؤثرة، وتلبي حاجة حقيقية في المجتمع الجزائري. وللانتقال إلى مرحلة تصميم البروتوتيب باستخدام Flutter، نقترح إثراء الفكرة من خلال تنظيم وتوسيع المحتوى والخدمات بشكل عملي ومناسب لتطبيق موبايل يعمل أوفلاين مع خصائص أونلاين عند الاتصال.

الأقسام الرئيسية للتطبيق
1. 🩺 الاستشارات الطبية الفورية
•	دردشة مباشرة أو مكالمات فيديو مع أطباء أطفال أو طوارئ.
•	واجهة لحجز استشارة مع اختيار التخصص (حروق، تسمم، سقوط...).
________________________________________
2. 🚑 دليل الإسعافات الأولية التفاعلي
•	واجهة مبسطة حسب نوع الحادث:
o	الحروق
o	التسمم
o	الاختناق
o	السقوط
o	النزيف
•	نصوص توضيحية قصيرة.
•	مثلاً: "ماذا تفعل في حال ابتلع الطفل بطارية؟").
________________________________________
3. ⏰ التنبيهات الصحية المخصصة
•	تقويم صحي مخصص للطفل حسب عمره.
•	تنبيهات تلقائية لـ:
o	التطعيمات (بناء على البرنامج الوطني الجزائري)
o	الفحوصات الدورية
o	نصائح موسمية (مثلاً: الوقاية من ضربات الشمس في الصيف).
________________________________________
4. 📚 مكتبة تعليمية وتدريبية
•	دروس مبسطة للأهالي حول:
o	سلامة المنزل (الكهرباء، أدوات المطبخ…)
o	إعداد حقيبة الإسعافات المنزلية.
o	التعامل النفسي مع الطفل وقت الأزمة.
•	Quiz تفاعلي لاختبار المعلومات.
________________________________________
5. 🗺️ خريطة الطوارئ
•	تحديد أقرب:
o	مستشفى أطفال
o	صيدلية مفتوحة
o	رقم الطوارئ المحلي (مباشرة من التطبيق)
•	دعم الوضع الليلي للطوارئ الليلية.
•	إشعار أو زر طوارئ للاتصال السريع بالهلال الأحمر أو الحماية المدنية.
________________________________________
🧠 إضافات مقترحة 
•	🧠 مساعد ذكي: للرد على الأسئلة الشائعة. 
Use this free api from open route
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
•	
•	👨‍👩‍👧 ملف طبي لكل طفل: يحتوي على بيانات طبية، أدوية، حساسيات.
________________________________________
📱 التصميم والتقنيات
•	تصميم مريح وهادئ، بألوان الأطفال (أزرق فاتح، أبيض، أخضر).
•	دعم RTL بالكامل.
•	ملفات البيانات بصيغة JSON في assets.
•	الصور والفيديوهات داخل التطبيق أو روابط للتحميل المرة الأولى.
•	معلومات تتركز حول ولاية سطيف في الجزائر 


# تعليمات عامة للذكاء الاصطناعي في Augment Code

- الهدف: بناء تطبيق Flutter باسم "سلامة طفلي" يعمل أوفلاين، ويُساعد الأهالي في الجزائر على حماية أطفالهم من الحوادث .
- اللغة: RTL (الواجهة باللغة العربية).
- التصميم: بسيط، مريح للعين، ألوان أطفال، يُفضل استخدام صور SVG.
- الداتا: تُخزن في ملفات JSON داخل مجلد `assets/data/`.
- عدم وضع النصوص داخل الكود مباشرة. استخدم ملفات JSON منفصلة أو ملفات ترجمة.
- استخدام state management بسيط (مثل Provider أو Riverpod).
- التأكد من توافق الكود مع build أوفلاين بالكامل.


