                        -HC:\flutter\packages\flutter_tools\gradle\src\main\scripts
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DANDROID_PLATFORM=android-21
-<PERSON><PERSON>DROID_ABI=x86_64
-DC<PERSON>KE_ANDROID_ARCH_ABI=x86_64
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\29.0.13599879
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\29.0.13599879
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\29.0.13599879\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\Development Yassine\flutter\Salamt_Tifli\build\app\intermediates\cxx\Debug\32i5s3g1\obj\x86_64
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\Development Yassine\flutter\Salamt_Tifli\build\app\intermediates\cxx\Debug\32i5s3g1\obj\x86_64
-DCMAKE_BUILD_TYPE=Debug
-BD:\Development Yassine\flutter\Salamt_Tifli\build\.cxx\Debug\32i5s3g1\x86_64
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2