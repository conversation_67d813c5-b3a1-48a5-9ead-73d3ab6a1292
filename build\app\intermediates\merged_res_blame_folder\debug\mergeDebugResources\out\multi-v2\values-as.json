{"logs": [{"outputFile": "com.salamt.tifli.salamt_tifli.app-mergeDebugResources-44:/values-as/values-as.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b83b8b00b8346c9e7414a1f1298f055d\\transformed\\preference-1.2.1\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,182,266,349,492,661,752", "endColumns": "76,83,82,142,168,90,79", "endOffsets": "177,261,344,487,656,747,827"}, "to": {"startLines": "54,56,60,61,64,65,66", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5713,5898,6302,6385,6711,6880,6971", "endColumns": "76,83,82,142,168,90,79", "endOffsets": "5785,5977,6380,6523,6875,6966,7046"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0d0ad2c9a7eee0ad2b557032bddebd70\\transformed\\appcompat-1.1.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,312,419,510,612,732,809,885,976,1068,1163,1257,1358,1451,1546,1640,1731,1822,1907,2020,2128,2227,2336,2452,2572,2739,2841", "endColumns": "107,98,106,90,101,119,76,75,90,91,94,93,100,92,94,93,90,90,84,112,107,98,108,115,119,166,101,81", "endOffsets": "208,307,414,505,607,727,804,880,971,1063,1158,1252,1353,1446,1541,1635,1726,1817,1902,2015,2123,2222,2331,2447,2567,2734,2836,2918"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,312,419,510,612,732,809,885,976,1068,1163,1257,1358,1451,1546,1640,1731,1822,1907,2020,2128,2227,2336,2452,2572,2739,6528", "endColumns": "107,98,106,90,101,119,76,75,90,91,94,93,100,92,94,93,90,90,84,112,107,98,108,115,119,166,101,81", "endOffsets": "208,307,414,505,607,727,804,880,971,1063,1158,1252,1353,1446,1541,1635,1726,1817,1902,2015,2123,2222,2331,2447,2567,2734,2836,6605"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0c69679757972620720ec039d7103818\\transformed\\browser-1.8.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,269,377", "endColumns": "107,105,107,105", "endOffsets": "158,264,372,478"}, "to": {"startLines": "55,57,58,59", "startColumns": "4,4,4,4", "startOffsets": "5790,5982,6088,6196", "endColumns": "107,105,107,105", "endOffsets": "5893,6083,6191,6297"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\66aa7f682cf61ffe3ee75db6ee238d77\\transformed\\core-1.13.1\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,259,367,472,576,676,805", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "151,254,362,467,571,671,800,901"}, "to": {"startLines": "29,30,31,32,33,34,35,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2841,2942,3045,3153,3258,3362,3462,6610", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "2937,3040,3148,3253,3357,3457,3586,6706"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\9d4e1de4e870e893108c546e2600c23f\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-as\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "121", "endOffsets": "316"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4562", "endColumns": "125", "endOffsets": "4683"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a3d296532cc5acbfa6d00cce05e38839\\transformed\\jetified-play-services-base-18.3.0\\res\\values-as\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,446,565,671,797,915,1024,1132,1271,1376,1522,1643,1772,1921,1977,2039", "endColumns": "103,148,118,105,125,117,108,107,138,104,145,120,128,148,55,61,81", "endOffsets": "296,445,564,670,796,914,1023,1131,1270,1375,1521,1642,1771,1920,1976,2038,2120"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3591,3699,3852,3975,4085,4215,4337,4450,4688,4831,4940,5090,5215,5348,5501,5561,5627", "endColumns": "107,152,122,109,129,121,112,111,142,108,149,124,132,152,59,65,85", "endOffsets": "3694,3847,3970,4080,4210,4332,4445,4557,4826,4935,5085,5210,5343,5496,5556,5622,5708"}}]}]}