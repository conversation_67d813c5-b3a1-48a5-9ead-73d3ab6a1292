// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ChatMessage _$ChatMessageFromJson(Map<String, dynamic> json) => ChatMessage(
  id: json['id'] as String,
  consultationId: json['consultationId'] as String,
  senderId: json['senderId'] as String,
  senderName: json['senderName'] as String,
  type: $enumDecode(_$MessageTypeEnumMap, json['type']),
  content: json['content'] as String,
  timestamp: DateTime.parse(json['timestamp'] as String),
  status: $enumDecode(_$MessageStatusEnumMap, json['status']),
  attachmentUrl: json['attachmentUrl'] as String?,
  attachmentType: json['attachmentType'] as String?,
);

Map<String, dynamic> _$ChatMessageToJson(ChatMessage instance) =>
    <String, dynamic>{
      'id': instance.id,
      'consultationId': instance.consultationId,
      'senderId': instance.senderId,
      'senderName': instance.senderName,
      'type': _$MessageTypeEnumMap[instance.type]!,
      'content': instance.content,
      'timestamp': instance.timestamp.toIso8601String(),
      'status': _$MessageStatusEnumMap[instance.status]!,
      'attachmentUrl': instance.attachmentUrl,
      'attachmentType': instance.attachmentType,
    };

const _$MessageTypeEnumMap = {
  MessageType.text: 'text',
  MessageType.image: 'image',
  MessageType.file: 'file',
  MessageType.system: 'system',
};

const _$MessageStatusEnumMap = {
  MessageStatus.sending: 'sending',
  MessageStatus.sent: 'sent',
  MessageStatus.delivered: 'delivered',
  MessageStatus.read: 'read',
  MessageStatus.failed: 'failed',
};
