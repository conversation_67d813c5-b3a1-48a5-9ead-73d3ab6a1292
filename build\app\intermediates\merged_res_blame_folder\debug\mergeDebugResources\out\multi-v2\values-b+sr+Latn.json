{"logs": [{"outputFile": "com.salamt.tifli.salamt_tifli.app-mergeDebugResources-44:/values-b+sr+Latn/values-b+sr+Latn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b83b8b00b8346c9e7414a1f1298f055d\\transformed\\preference-1.2.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,263,343,495,664,751", "endColumns": "70,86,79,151,168,86,82", "endOffsets": "171,258,338,490,659,746,829"}, "to": {"startLines": "54,65,69,70,73,74,75", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5737,6591,6989,7069,7408,7577,7664", "endColumns": "70,86,79,151,168,86,82", "endOffsets": "5803,6673,7064,7216,7572,7659,7742"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a3d296532cc5acbfa6d00cce05e38839\\transformed\\jetified-play-services-base-18.3.0\\res\\values-b+sr+Latn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "200,303,457,580,686,836,959,1067,1165,1310,1413,1569,1692,1837,1976,2040,2101", "endColumns": "102,153,122,105,149,122,107,97,144,102,155,122,144,138,63,60,75", "endOffsets": "302,456,579,685,835,958,1066,1164,1309,1412,1568,1691,1836,1975,2039,2100,2176"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3562,3669,3827,3954,4064,4218,4345,4457,4689,4838,4945,5105,5232,5381,5524,5592,5657", "endColumns": "106,157,126,109,153,126,111,101,148,106,159,126,148,142,67,64,79", "endOffsets": "3664,3822,3949,4059,4213,4340,4452,4554,4833,4940,5100,5227,5376,5519,5587,5652,5732"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0c69679757972620720ec039d7103818\\transformed\\browser-1.8.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,266,379", "endColumns": "110,99,112,97", "endOffsets": "161,261,374,472"}, "to": {"startLines": "55,66,67,68", "startColumns": "4,4,4,4", "startOffsets": "5808,6678,6778,6891", "endColumns": "110,99,112,97", "endOffsets": "5914,6773,6886,6984"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\66c3f8d759689e7c8bf8d566a47d4905\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,191,256,328,407,480,568,652", "endColumns": "74,60,64,71,78,72,87,83,74", "endOffsets": "125,186,251,323,402,475,563,647,722"}, "to": {"startLines": "56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5919,5994,6055,6120,6192,6271,6344,6432,6516", "endColumns": "74,60,64,71,78,72,87,83,74", "endOffsets": "5989,6050,6115,6187,6266,6339,6427,6511,6586"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\9d4e1de4e870e893108c546e2600c23f\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-b+sr+Latn\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "125", "endOffsets": "327"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4559", "endColumns": "129", "endOffsets": "4684"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\66aa7f682cf61ffe3ee75db6ee238d77\\transformed\\core-1.13.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "29,30,31,32,33,34,35,72", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2836,2934,3036,3133,3237,3341,3446,7307", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "2929,3031,3128,3232,3336,3441,3557,7403"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0d0ad2c9a7eee0ad2b557032bddebd70\\transformed\\appcompat-1.1.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,816,899,990,1082,1177,1271,1372,1465,1560,1665,1756,1847,1932,2037,2143,2246,2353,2462,2569,2739,2836", "endColumns": "106,100,105,85,103,121,84,82,90,91,94,93,100,92,94,104,90,90,84,104,105,102,106,108,106,169,96,85", "endOffsets": "207,308,414,500,604,726,811,894,985,1077,1172,1266,1367,1460,1555,1660,1751,1842,1927,2032,2138,2241,2348,2457,2564,2734,2831,2917"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,816,899,990,1082,1177,1271,1372,1465,1560,1665,1756,1847,1932,2037,2143,2246,2353,2462,2569,2739,7221", "endColumns": "106,100,105,85,103,121,84,82,90,91,94,93,100,92,94,104,90,90,84,104,105,102,106,108,106,169,96,85", "endOffsets": "207,308,414,500,604,726,811,894,985,1077,1172,1266,1367,1460,1555,1660,1751,1842,1927,2032,2138,2241,2348,2457,2564,2734,2831,7302"}}]}]}